import { prisma } from '../../config/prismaClient.js';

/**
 * Products Service
 * All database operations for products management
 * Uses exact field names from the Products Prisma model
 */

/**
 * Get products with filtering and pagination
 */
export const getProductsService = async (filters) => {
  const { page, limit, search, category, supplierId, inStock, sort, order } = filters;
  
  // Calculate pagination
  const skip = (page - 1) * limit;
  
  // Build where clause
  const whereClause = {
    Deleted: false
  };
  
  // Add search filter (search in Name and SKU)
  if (search && search.trim()) {
    whereClause.OR = [
      {
        Name: {
          contains: search.trim(),
          mode: 'insensitive'
        }
      },
      {
        SKU: {
          contains: search.trim(),
          mode: 'insensitive'
        }
      }
    ];
  }
  
  // Add category filter
  if (category) {
    whereClause.CategoryId = category;
  }
  
  // Add supplier filter
  if (supplierId) {
    whereClause.SupplierId = supplierId;
  }
  
  // Add stock filter
  if (inStock !== undefined) {
    if (inStock) {
      whereClause.Stock = {
        gt: 0
      };
    } else {
      whereClause.OR = [
        { Stock: { lte: 0 } },
        { Stock: null }
      ];
    }
  }
  
  // Build order by clause
  const orderBy = {};
  orderBy[sort] = order;
  
  // Execute query
  const [products, total] = await Promise.all([
    prisma.products.findMany({
      where: whereClause,
      include: {
        Categories: {
          select: {
            ID: true,
            Name: true,
            Description: true
          }
        },
        Suppliers: {
          include: {
            Users: {
              select: {
                Id: true,
                Name: true,
                Email: true
              }
            }
          }
        },
        Customer: {
          include: {
            Users: {
              select: {
                Id: true,
                Name: true,
                Email: true
              }
            }
          }
        },
        Images: {
          where: { Deleted: false },
          select: {
            ID: true,
            Url: true
          }
        },
        ProductAttribute: {
          where: { Deleted: false },
          select: {
            ID: true,
            Key: true,
            Value: true
          }
        },
        ProductVariant: {
          where: { Deleted: false },
          select: {
            ID: true,
            Name: true,
            Type: true,
            CustomPrice: true,
            Stock: true
          }
        }
      },
      orderBy,
      skip,
      take: limit
    }),
    prisma.products.count({
      where: whereClause
    })
  ]);
  
  return {
    products,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
};

/**
 * Get single product by ID
 */
export const getProductByIdService = async (productId) => {
  const product = await prisma.products.findUnique({
    where: {
      ID: productId,
      Deleted: false
    },
    include: {
      Categories: {
        select: {
          ID: true,
          Name: true,
          Description: true
        }
      },
      Suppliers: {
        include: {
          Users: {
            select: {
              Id: true,
              Name: true,
              Email: true,
              PhoneNumber: true
            }
          }
        }
      },
      Customer: {
        include: {
          Users: {
            select: {
              Id: true,
              Name: true,
              Email: true
            }
          }
        }
      },
      Images: {
        where: { Deleted: false },
        select: {
          ID: true,
          Url: true
        }
      },
      ProductAttribute: {
        where: { Deleted: false },
        select: {
          ID: true,
          Key: true,
          Value: true
        }
      },
      ProductVariant: {
        where: { Deleted: false },
        select: {
          ID: true,
          Name: true,
          Type: true,
          CustomPrice: true,
          Stock: true
        }
      },
      Reviews: {
        where: { Deleted: false },
        include: {
          Customer: {
            include: {
              Users: {
                select: {
                  Id: true,
                  Name: true
                }
              }
            }
          }
        },
        select: {
          ID: true,
          Rate: true,
          Body: true,
          CreatedDate: true,
          Customer: true
        }
      }
    }
  });
  
  return product;
};

/**
 * Create new product
 */
export const createProductService = async (productData) => {
  const { Attributes, Variants, ...productFields } = productData;
  
  // Verify category exists
  if (productFields.CategoryId) {
    const category = await prisma.categories.findUnique({
      where: { ID: productFields.CategoryId, Deleted: false }
    });
    if (!category) {
      throw new Error('Category not found');
    }
  }
  
  // Verify supplier exists if provided
  if (productFields.SupplierId) {
    const supplier = await prisma.suppliers.findUnique({
      where: { Id: productFields.SupplierId }
    });
    if (!supplier) {
      throw new Error('Supplier not found');
    }
  }
  
  // Verify customer exists if provided
  if (productFields.CustomerId) {
    const customer = await prisma.customer.findUnique({
      where: { Id: productFields.CustomerId }
    });
    if (!customer) {
      throw new Error('Customer not found');
    }
  }
  
  // Create product with related data
  const product = await prisma.products.create({
    data: {
      ...productFields,
      Deleted: false,
      CreatedDate: new Date(),
      // Create attributes if provided
      ...(Attributes && Attributes.length > 0 && {
        ProductAttribute: {
          create: Attributes.map(attr => ({
            Key: attr.Key,
            Value: attr.Value,
            Deleted: false,
            CreatedDate: new Date()
          }))
        }
      }),
      // Create variants if provided
      ...(Variants && Variants.length > 0 && {
        ProductVariant: {
          create: Variants.map(variant => ({
            Name: variant.Name,
            Type: variant.Type,
            CustomPrice: variant.CustomPrice,
            Stock: variant.Stock,
            Deleted: false,
            CreatedDate: new Date()
          }))
        }
      })
    },
    include: {
      Categories: true,
      Suppliers: {
        include: {
          Users: {
            select: {
              Id: true,
              Name: true,
              Email: true
            }
          }
        }
      },
      Customer: {
        include: {
          Users: {
            select: {
              Id: true,
              Name: true,
              Email: true
            }
          }
        }
      },
      Images: {
        where: { Deleted: false }
      },
      ProductAttribute: {
        where: { Deleted: false }
      },
      ProductVariant: {
        where: { Deleted: false }
      }
    }
  });
  
  return product;
};

/**
 * Update existing product
 */
export const updateProductService = async (productId, updateData) => {
  // Check if product exists
  const existingProduct = await prisma.products.findUnique({
    where: { ID: productId, Deleted: false }
  });

  if (!existingProduct) {
    throw new Error('Product not found');
  }

  // Verify category exists if being updated
  if (updateData.CategoryId) {
    const category = await prisma.categories.findUnique({
      where: { ID: updateData.CategoryId, Deleted: false }
    });
    if (!category) {
      throw new Error('Category not found');
    }
  }

  // Verify supplier exists if being updated
  if (updateData.SupplierId) {
    const supplier = await prisma.suppliers.findUnique({
      where: { Id: updateData.SupplierId }
    });
    if (!supplier) {
      throw new Error('Supplier not found');
    }
  }

  // Verify customer exists if being updated
  if (updateData.CustomerId) {
    const customer = await prisma.customer.findUnique({
      where: { Id: updateData.CustomerId }
    });
    if (!customer) {
      throw new Error('Customer not found');
    }
  }

  // Update product
  const product = await prisma.products.update({
    where: { ID: productId },
    data: {
      ...updateData,
      UpdatedDate: new Date()
    },
    include: {
      Categories: true,
      Suppliers: {
        include: {
          Users: {
            select: {
              Id: true,
              Name: true,
              Email: true
            }
          }
        }
      },
      Customer: {
        include: {
          Users: {
            select: {
              Id: true,
              Name: true,
              Email: true
            }
          }
        }
      },
      Images: {
        where: { Deleted: false }
      },
      ProductAttribute: {
        where: { Deleted: false }
      },
      ProductVariant: {
        where: { Deleted: false }
      }
    }
  });

  return product;
};

/**
 * Delete product (soft delete)
 */
export const deleteProductService = async (productId) => {
  // Check if product exists
  const existingProduct = await prisma.products.findUnique({
    where: { ID: productId, Deleted: false }
  });

  if (!existingProduct) {
    throw new Error('Product not found');
  }

  // Soft delete product and related data
  await prisma.$transaction(async (tx) => {
    // Delete product
    await tx.products.update({
      where: { ID: productId },
      data: {
        Deleted: true,
        UpdatedDate: new Date()
      }
    });

    // Delete related images
    await tx.images.updateMany({
      where: { ProductId: productId },
      data: {
        Deleted: true,
        UpdatedDate: new Date()
      }
    });

    // Delete related attributes
    await tx.productAttribute.updateMany({
      where: { ProductId: productId },
      data: {
        Deleted: true,
        UpdatedDate: new Date()
      }
    });

    // Delete related variants
    await tx.productVariant.updateMany({
      where: { ProductId: productId },
      data: {
        Deleted: true,
        UpdatedDate: new Date()
      }
    });
  });

  return true;
};

/**
 * Upload product images
 */
export const uploadProductImagesService = async (productId, imageUrls) => {
  // Check if product exists
  const existingProduct = await prisma.products.findUnique({
    where: { ID: productId, Deleted: false }
  });

  if (!existingProduct) {
    throw new Error('Product not found');
  }

  // Create image records
  const images = await prisma.images.createMany({
    data: imageUrls.map(url => ({
      Url: url,
      ProductId: productId,
      Deleted: false,
      CreatedDate: new Date()
    }))
  });

  // Return the created image URLs
  return imageUrls;
};
