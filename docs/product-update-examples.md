# Product Update API Examples

## Overview
The product update API now supports updating attributes and variants along with basic product information.

## Update Product with Attributes and Variants

### Endpoint
```
PUT /api/products/:id
```

### Request Body Structure

#### Basic Product Update
```json
{
  "Name": "Updated Product Name",
  "Description": "Updated description",
  "Price": 29.99,
  "Stock": 100,
  "MinimumStock": 10,
  "CategoryId": 2,
  "SupplierId": "supplier-guid-here",
  "CustomerId": "customer-guid-here"
}
```

#### Update with Attributes
```json
{
  "Name": "Updated Product Name",
  "Price": 29.99,
  "Attributes": [
    {
      "_action": "create",
      "Key": "Color",
      "Value": "Blue"
    },
    {
      "_action": "update",
      "ID": 123,
      "Key": "Size",
      "Value": "Large"
    },
    {
      "_action": "delete",
      "ID": 124
    }
  ]
}
```

#### Update with Variants
```json
{
  "Name": "Updated Product Name",
  "Price": 29.99,
  "Variants": [
    {
      "_action": "create",
      "Name": "Small Size",
      "Type": "Size",
      "CustomPrice": 25.99,
      "Stock": 50
    },
    {
      "_action": "update",
      "ID": 456,
      "Name": "Medium Size",
      "Type": "Size",
      "CustomPrice": 29.99,
      "Stock": 75
    },
    {
      "_action": "delete",
      "ID": 457
    }
  ]
}
```

#### Complete Update Example
```json
{
  "Name": "Premium T-Shirt",
  "Description": "High-quality cotton t-shirt",
  "Price": 39.99,
  "Stock": 200,
  "MinimumStock": 20,
  "CategoryId": 1,
  "Attributes": [
    {
      "_action": "create",
      "Key": "Material",
      "Value": "100% Cotton"
    },
    {
      "_action": "update",
      "ID": 100,
      "Key": "Brand",
      "Value": "Premium Brand"
    }
  ],
  "Variants": [
    {
      "_action": "create",
      "Name": "Small",
      "Type": "Size",
      "CustomPrice": 35.99,
      "Stock": 50
    },
    {
      "_action": "create",
      "Name": "Medium",
      "Type": "Size",
      "CustomPrice": 39.99,
      "Stock": 75
    },
    {
      "_action": "update",
      "ID": 200,
      "Name": "Large",
      "Type": "Size",
      "CustomPrice": 42.99,
      "Stock": 60
    }
  ]
}
```

## Action Types

### For Attributes and Variants:
- **`create`**: Creates a new attribute/variant (ID not required)
- **`update`**: Updates existing attribute/variant (ID required)
- **`delete`**: Soft deletes attribute/variant (ID required)

### Notes:
1. If `_action` is not specified, it defaults to `create`
2. For `update` and `delete` actions, the `ID` field is required
3. For `create` action, the `ID` field is ignored if provided
4. All operations are performed within a database transaction
5. If any operation fails, the entire update is rolled back

## Response Format
The API returns the updated product with all related data:

```json
{
  "success": true,
  "message": "Product updated successfully",
  "data": {
    "id": 123,
    "name": "Premium T-Shirt",
    "description": "High-quality cotton t-shirt",
    "price": 39.99,
    "stock": 200,
    "minimumStock": 20,
    "sku": "generated-sku",
    "categoryId": 1,
    "supplierId": null,
    "customerId": null,
    "image": "first-image-url",
    "images": ["image-url-1", "image-url-2"],
    "category": {
      "id": 1,
      "name": "Clothing",
      "description": "Clothing items"
    },
    "supplier": null,
    "customer": null,
    "attributes": [
      {
        "id": 100,
        "key": "Brand",
        "value": "Premium Brand"
      },
      {
        "id": 101,
        "key": "Material",
        "value": "100% Cotton"
      }
    ],
    "variants": [
      {
        "id": 200,
        "name": "Large",
        "type": "Size",
        "price": 42.99,
        "stock": 60
      },
      {
        "id": 201,
        "name": "Small",
        "type": "Size",
        "price": 35.99,
        "stock": 50
      }
    ],
    "reviews": [],
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-02T00:00:00.000Z"
  }
}
```
