# Product Image Deletion API Examples

## Overview
The new image deletion endpoint allows you to delete specific images from a product. This performs both database soft-delete and Cloudinary cleanup.

## Endpoint Details

### Delete Product Image
```
DELETE /api/products/:productId/images/:imageId
```

## Usage Examples

### 1. Basic Image Deletion

**Request:**
```http
DELETE /api/products/220/images/45
Authorization: Bearer your-jwt-token
```

**Success Response:**
```json
{
  "success": true,
  "message": "Image deleted successfully",
  "data": {
    "imageId": 45,
    "productId": 220,
    "imageUrl": "https://res.cloudinary.com/dbgfyigqr/image/upload/v1234567890/products/product_220_1703123456789_0.jpg",
    "cloudinaryDeleted": true,
    "cloudinaryPublicId": "products/product_220_1703123456789_0"
  }
}
```

### 2. Error Cases

**Product Not Found:**
```json
{
  "success": false,
  "message": "Product not found"
}
```

**Image Not Found:**
```json
{
  "success": false,
  "message": "Image not found or does not belong to this product"
}
```

**Invalid Parameters:**
```json
{
  "success": false,
  "message": "\"productId\" must be a number"
}
```

## How to Get Image IDs

To find the image IDs for deletion, first get the product details:

**Request:**
```http
GET /api/products/220
Authorization: Bearer your-jwt-token
```

**Response (showing images with IDs):**
```json
{
  "success": true,
  "message": "Product retrieved successfully",
  "data": {
    "id": 220,
    "name": "Sample Product",
    "images": [
      "https://res.cloudinary.com/dbgfyigqr/image/upload/v1234567890/products/image1.jpg",
      "https://res.cloudinary.com/dbgfyigqr/image/upload/v1234567890/products/image2.jpg"
    ],
    // ... other product data
  }
}
```

**Note:** You'll need to modify the product response mapping to include image IDs. Currently, only URLs are returned.

## Implementation Details

### What Happens When You Delete an Image:

1. **Validation**: Checks if product and image exist and belong together
2. **Database Update**: Marks the image as deleted (soft delete)
3. **Cloudinary Cleanup**: Attempts to delete the image from Cloudinary
4. **Response**: Returns deletion status and details

### Safety Features:

- **Soft Delete**: Images are marked as deleted, not permanently removed
- **Ownership Check**: Ensures image belongs to the specified product
- **Graceful Degradation**: Database operation succeeds even if Cloudinary deletion fails
- **Authentication**: Requires valid JWT token
- **Validation**: Validates all parameters before processing

### Cloudinary Integration:

The endpoint automatically:
- Extracts the public ID from the Cloudinary URL
- Calls Cloudinary's deletion API
- Reports whether Cloudinary deletion was successful
- Continues operation even if Cloudinary deletion fails

## Testing with Postman/Thunder Client

### Step 1: Get Product with Images
```
GET http://localhost:3000/api/products/220
Authorization: Bearer your-jwt-token
```

### Step 2: Note the Image URLs and Extract Image IDs
You'll need to query the database directly or modify the API to return image IDs.

### Step 3: Delete Specific Image
```
DELETE http://localhost:3000/api/products/220/images/45
Authorization: Bearer your-jwt-token
```

### Step 4: Verify Deletion
```
GET http://localhost:3000/api/products/220
Authorization: Bearer your-jwt-token
```

The deleted image should no longer appear in the images array.

## Recommended Enhancement

Consider modifying the product response to include image IDs:

```json
{
  "images": [
    {
      "id": 45,
      "url": "https://res.cloudinary.com/dbgfyigqr/image/upload/v1234567890/products/image1.jpg"
    },
    {
      "id": 46,
      "url": "https://res.cloudinary.com/dbgfyigqr/image/upload/v1234567890/products/image2.jpg"
    }
  ]
}
```

This would make it easier for frontend applications to manage image deletion.
